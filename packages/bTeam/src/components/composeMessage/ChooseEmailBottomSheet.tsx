import React, { <PERSON><PERSON><PERSON>, SetStateAction, useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";

// Components
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  Button,
  SearchInput,
  AvatarEmailItem,
  CustomText,
  IconButton,
} from "b-ui-lib";
import { UserUniboxes } from "../../types/userMails";

type Props = {
  userUniboxes: UserUniboxes;
  selectedRecipientEmailId: string;
  setSelectedRecipientEmailId: Dispatch<SetStateAction<string>>;
  handleCloseBottomSheet: () => void;
};

const ChooseEmailBottomSheet: React.FC<Props> = ({
  userUniboxes,
  selectedRecipientEmailId,
  setSelectedRecipientEmailId,
  handleCloseBottomSheet,
}) => {
  const [searchInputValue, setSearchInputValue] = useState<string>("");
  const { styles } = useThemeAwareObject(createStyles);

  // Convert userUniboxes to the format expected by AvatarEmailItem
  const emailOptions = useMemo(() => {
    return userUniboxes.allIds.map((id) => {
      const unibox = userUniboxes.byId[id];
      return {
        id: id,
        value: unibox?.value || "",
        avatarName: unibox?.avatarName || "",
        avatarBackgroundColor: "#007AFF", // Default blue color
      };
    });
  }, [userUniboxes]);

  // Filter emails based on search input
  const filteredEmails = useMemo(() => {
    if (!searchInputValue) {
      return emailOptions;
    }

    return emailOptions.filter((email) =>
      email.value.toLowerCase().includes(searchInputValue.toLowerCase())
    );
  }, [emailOptions, searchInputValue]);

  const handleSelectEmail = (emailId: string) => {
    setSelectedRecipientEmailId(emailId);
  };

  const handleAddButton = () => {
    handleCloseBottomSheet();
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", paddingVertical: 16 }}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <CustomText style={{ fontSize: 20, fontWeight: "700", marginRight: 4 }}>Choose</CustomText>
            <CustomText style={{ fontSize: 20 }}>Email</CustomText>
          </View>
          <IconButton
            name="x"
            size={24}
            color="#666"
            onPress={handleCloseBottomSheet}
          />
        </View>

        <SearchInput
          placeholder="Search for an email"
          value={searchInputValue}
          onChangeText={(text) => setSearchInputValue(text)}
          handleInputClear={() => setSearchInputValue("")}
        />

        <ScrollView style={styles.scrollViewContainer}>
          {filteredEmails.map((email) => (
            <AvatarEmailItem
              key={email.id}
              email={email}
              handleSelectEmail={handleSelectEmail}
              isSelected={email.id === selectedRecipientEmailId}
            />
          ))}
        </ScrollView>

        <Button title="Add" onPress={handleAddButton} />
      </View>
    </GestureHandlerRootView>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: SPACING.S,
      paddingBottom: SPACING.S,
    },
    scrollViewContainer: {
      flex: 1,
      marginVertical: SPACING.S,
    },
  });

  return { styles, color };
};

export default ChooseEmailBottomSheet;
