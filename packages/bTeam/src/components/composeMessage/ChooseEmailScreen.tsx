import React, { Dispatch, SetStateAction, useMemo, useState } from "react";
import { StyleSheet, View, FlatList, Pressable } from "react-native";

// Components
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  Button,
  SearchInput,
  CustomText,
  IconButton,
  FONT_SIZES,
  Avatar,
} from "b-ui-lib";
import { UserUniboxes } from "../../types/userMails";

type Props = {
  userUniboxes: UserUniboxes;
  selectedRecipientEmailId: string;
  setSelectedRecipientEmailId: Dispatch<SetStateAction<string>>;
  onClose: () => void;
};

type EmailOption = {
  id: string;
  value: string;
  avatarName: string;
  avatarBackgroundColor: string;
};

const ChooseEmailScreen: React.FC<Props> = ({
  userUniboxes,
  selectedRecipientEmailId,
  setSelectedRecipientEmailId,
  onClose,
}) => {
  const [searchInputValue, setSearchInputValue] = useState<string>("");
  const { styles, color } = useThemeAwareObject(createStyles);

  // Convert userUniboxes to the format expected by the list
  const emailOptions = useMemo(() => {
    return userUniboxes.allIds.map((id) => {
      const unibox = userUniboxes.byId[id];
      return {
        id: id,
        value: unibox?.value || "",
        avatarName: unibox?.avatarName || "",
        avatarBackgroundColor: "#007AFF", // Default blue color
      };
    });
  }, [userUniboxes]);

  // Filter emails based on search input
  const filteredEmails = useMemo(() => {
    if (!searchInputValue) {
      return emailOptions;
    }

    return emailOptions.filter((email) =>
      email.value.toLowerCase().includes(searchInputValue.toLowerCase())
    );
  }, [emailOptions, searchInputValue]);

  const handleSelectEmail = (emailId: string) => {
    setSelectedRecipientEmailId(emailId);
  };

  const handleAddButton = () => {
    onClose();
  };

  const renderEmailItem = ({ item }: { item: EmailOption }) => (
    <Pressable
      style={[
        styles.emailItem,
        item.id === selectedRecipientEmailId && styles.selectedEmailItem,
      ]}
      onPress={() => handleSelectEmail(item.id)}
    >
      <View style={styles.emailItemContent}>
        <Avatar
          name={item.avatarName}
          isSmall
          style={{ container: styles.avatar }}
        />
        <CustomText style={styles.emailText}>{item.value}</CustomText>
      </View>
      {item.id === selectedRecipientEmailId && (
        <IconButton
          name="check"
          size={20}
          color={color.MESSAGE_FLAG}
        />
      )}
    </Pressable>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <CustomText style={styles.headerTitleBold}>Choose</CustomText>
          <CustomText style={styles.headerTitle}>Email</CustomText>
        </View>
        <IconButton
          name="x"
          size={24}
          color={color.TEXT_DEFAULT}
          onPress={onClose}
        />
      </View>

      {/* Search Input */}
      <SearchInput
        placeholder="Search for an email"
        value={searchInputValue}
        onChangeText={(text) => setSearchInputValue(text)}
        handleInputClear={() => setSearchInputValue("")}
        containerStyle={styles.searchContainer}
      />

      {/* Email List */}
      <FlatList
        data={filteredEmails}
        renderItem={renderEmailItem}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Add Button */}
      <View style={styles.buttonContainer}>
        <Button title="Add" onPress={handleAddButton} />
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      paddingHorizontal: SPACING.S,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: SPACING.M,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER,
    },
    headerTitleContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerTitleBold: {
      fontSize: FONT_SIZES.TWENTY,
      fontWeight: "700",
      color: color.TEXT_DEFAULT,
      marginRight: SPACING.XXS,
    },
    headerTitle: {
      fontSize: FONT_SIZES.TWENTY,
      color: color.TEXT_DEFAULT,
    },
    searchContainer: {
      marginVertical: SPACING.S,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingBottom: SPACING.S,
    },
    emailItem: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.S,
      marginVertical: SPACING.XXS,
      borderRadius: SPACING.XS,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    selectedEmailItem: {
      backgroundColor: color.PRESSABLE,
      borderWidth: 1,
      borderColor: color.MESSAGE_FLAG,
    },
    emailItemContent: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    avatar: {
      marginRight: SPACING.S,
    },
    emailText: {
      fontSize: FONT_SIZES.FOURTEEN,
      color: color.TEXT_DEFAULT,
      flex: 1,
    },
    buttonContainer: {
      paddingVertical: SPACING.S,
      paddingBottom: SPACING.L,
    },
  });

  return { styles, color };
};

export default ChooseEmailScreen;
