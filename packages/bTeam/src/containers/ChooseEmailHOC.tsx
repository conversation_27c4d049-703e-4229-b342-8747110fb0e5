import React from "react";
import { useSelector } from "react-redux";
import { NavigationProp, ParamListBase, useNavigation } from "@react-navigation/native";

// Components
import ChooseEmailScreen from "../components/composeMessage/ChooseEmailScreen";



type Props = {
  route: {
    params: {
      selectedRecipientEmailId: string;
      onEmailSelected: (emailId: string) => void;
    };
  };
};

const ChooseEmailHOC: React.FC<Props> = ({ route }) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { selectedRecipientEmailId, onEmailSelected } = route.params;

  // Get user uniboxes from Redux store
  const { userUniboxes } = useSelector((state: any) => state.persist?.bTeamUsers || {});

  const handleEmailSelection = (emailId: string) => {
    // Call the callback function passed from the compose message screen
    onEmailSelected(emailId);
  };

  const handleClose = () => {
    navigation.goBack();
  };

  // Provide default structure if userUniboxes is undefined
  const safeUserUniboxes = userUniboxes || {
    byId: {},
    allIds: [],
    isLoading: false,
    error: ""
  };

  return (
    <ChooseEmailScreen
      userUniboxes={safeUserUniboxes}
      selectedRecipientEmailId={selectedRecipientEmailId}
      setSelectedRecipientEmailId={handleEmailSelection}
      onClose={handleClose}
    />
  );
};

export default ChooseEmailHOC;
