import React from "react";
import { useSelector } from "react-redux";
import { Navigation<PERSON>rop, ParamListBase, useNavigation } from "@react-navigation/native";

// Components
import ChooseEmailScreen from "../components/composeMessage/ChooseEmailScreen";

// Types
import { RootState } from "../store/store";

type Props = {
  route: {
    params: {
      selectedRecipientEmailId: string;
      onEmailSelected: (emailId: string) => void;
    };
  };
};

const ChooseEmailHOC: React.FC<Props> = ({ route }) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { selectedRecipientEmailId, onEmailSelected } = route.params;

  // Get user uniboxes from Redux store
  const userUniboxes = useSelector((state: RootState) => state.users.userUniboxes);

  const handleEmailSelection = (emailId: string) => {
    // Call the callback function passed from the compose message screen
    onEmailSelected(emailId);
  };

  const handleClose = () => {
    navigation.goBack();
  };

  return (
    <ChooseEmailScreen
      userUniboxes={userUniboxes}
      selectedRecipientEmailId={selectedRecipientEmailId}
      setSelectedRecipientEmailId={handleEmailSelection}
      onClose={handleClose}
    />
  );
};

export default ChooseEmailHOC;
